{"manifest_version": 2, "name": "Trilium Web Clipper (dev)", "version": "1.0.1", "description": "Save web clippings to Trilium Notes.", "homepage_url": "https://github.com/zadam/trilium-web-clipper", "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "icons": {"32": "icons/32.png", "48": "icons/48.png", "96": "icons/96.png"}, "permissions": ["activeTab", "tabs", "http://*/", "https://*/", "<all_urls>", "storage", "contextMenus"], "browser_action": {"default_icon": "icons/32.png", "default_title": "Trilium Web Clipper", "default_popup": "popup/popup.html"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["lib/browser-polyfill.js", "utils.js", "content.js"]}], "background": {"scripts": ["lib/browser-polyfill.js", "utils.js", "trilium_server_facade.js", "background.js"]}, "options_ui": {"page": "options/options.html"}, "commands": {"saveSelection": {"description": "Save the selected text into a note", "suggested_key": {"default": "Ctrl+Shift+S"}}, "saveWholePage": {"description": "Save the current page", "suggested_key": {"default": "Alt+Shift+S"}}, "saveCroppedScreenshot": {"description": "Take a cropped screenshot of the current page", "suggested_key": {"default": "Ctrl+Shift+E"}}}, "browser_specific_settings": {"gecko": {"id": "{1410742d-b377-40e7-a9db-63dc9c6ec99c}"}}}