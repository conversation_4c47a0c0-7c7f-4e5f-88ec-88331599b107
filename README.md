# Trilium Web Clipper

## This repo is dead

**Trilium is in maintenance mode and Web Clipper is not likely to get new releases.**

Trilium Web Clipper is a web browser extension which allows user to clip text, screenshots, whole pages and short notes and save them directly to [Trilium Notes](https://github.com/zadam/trilium). 

For more details, see the [wiki page](https://github.com/zadam/trilium/wiki/Web-clipper).

## Keyboard shortcuts
Keyboard shortcuts are available for most functions:  
* Save selected text: `Ctrl+Shift+S` (Mac: `Cmd+Shift+S`)
* Save whole page: `Alt+Shift+S` (Mac: `Opt+Shift+S`)
* Save screenshot: `Ctrl+Shift+E` (Mac: `Cmd+Shift+E`)

To set custom shortcuts, follow the directions for your browser.

**Firefox**: `about:addons` > Gear icon ⚙️ > Manage extension shortcuts

**Chrome**: `chrome://extensions/shortcuts`

## Credits
Some parts of the code are based on the [Joplin Notes browser extension](https://github.com/laurent22/joplin/tree/master/Clipper).
