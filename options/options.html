<!DOCTYPE html>

<html>
<head>
    <meta content="text/html;charset=utf-8" http-equiv="Content-Type">
    <meta content="utf-8" http-equiv="encoding">
</head>

<body>

<div id="error-message" style="font-weight: bold; color: red; display: none;"></div>
<div id="success-message" style="font-weight: bold; color: green; display: none;"></div>

<h2>Trilium desktop instance</h2>

<p>Web clipper by default tries to find a running desktop instance on port 37740. If you configured your Trilium desktop app to run on a different port, you can specify it here (otherwise keep it empty).</p>

<form id="trilium-desktop-setup-form">
    <p>
        <label for="trilium-desktop-port" style="font-weight: bold;">Trilium desktop port: </label>

        <input type="text" id="trilium-desktop-port" size="6" style="text-align: right;"/> (normally keep this empty)
    </p>

    <input type="submit" value="Save">
</form>

<h2>Trilium server instance</h2>

<p>If you have a server instance set up, you can optionally configure it as a fail over target for the clipped notes. Desktop instance will still be given priority, but in cases that the desktop instance is not available (e.g. it's not running), web clipper will send the notes to the server instance instead.</p>

<div id="trilium-server-configured" style="display: none;">
    <strong>Trilium server instance has been already configured to <a id="trilium-server-link" href=""></a>.</strong>

    <p>You can also <a id="reset-trilium-server-setup" href="javascript:">remove the current setup</a> and configure it again.</p>
</div>

<form id="trilium-server-setup-form" style="display: none;">
    <table>
        <tr>
            <th>Trilium server URL:</th>
            <td><input type="text" id="trilium-server-url"/></td>
        </tr>
        <tr>
            <th>Password:</th>
            <td><input type="password" id="trilium-server-password"/></td>
        </tr>
        <tr>
            <th></th>
            <td><input type="submit" value="Login to the server instance"/></td>
        </tr>
    </table>

    <p>Note that the entered password is not stored anywhere, it will be only used to retrieve an authorization token from the server instance which will be then used to send the clipped notes.</p>
</form>

<script src="../lib/cash.min.js"></script>
<script src="../lib/browser-polyfill.js"></script>
<script src="options.js"></script>

</body>

</html>
