<!DOCTYPE html>

<html>
  <head>
    <meta content="text/html;charset=utf-8" http-equiv="Content-Type">
    <meta content="utf-8" http-equiv="encoding">
    <link rel="stylesheet" href="popup.css"/>
  </head>

<body>
  <div style="display: flex; justify-content: space-between; vertical-align: middle;">
    <h3>Trilium Web Clipper</h3>
    

    <div style="position: relative; top: 6px;">
      <button class="button" id="show-options-button">Options</button>

      <button class="button" id="show-help-button">Help</button>
    </div>
  </div>
  <div id="already-visited"></div>

  <button class="button full needs-connection" id="save-cropped-screenshot-button">Crop screen shot</button>
  <button class="button full needs-connection" id="save-whole-screenshot-button">Save whole screen shot</button>
  <button class="button full needs-connection" id="save-whole-page-button">Save whole page</button>
  <button class="button full needs-connection" id="save-link-with-note-button">Save link with a note</button>
  <button class="button full needs-connection" id="save-tabs-button">Save window's tabs as a list</button>

  <div id="save-link-with-note-wrapper">
    <textarea id="save-link-with-note-textarea" rows="5"></textarea>

    <div>
      <input type="checkbox" id="keep-title-checkbox" name="keep-title-checkbox" value="Bike">
      <label for="keep-title-checkbox">Keep page title as note title</label><br>
    </div>
    <div style="display: flex;">
      <button type="submit" class="button wide" id="save-button">Save</button>

      <button type="submit" class="button wide" id="cancel-button">Cancel</button>
    </div>
  </div>

  <div style="margin-top: 15px;">
    <button class="button" id="check-connection-button">check</button>

    <div>Status: <span id="connection-status">unknown</span></div>
  </div>

  <script src="../lib/browser-polyfill.js"></script>
  <script src="../lib/cash.min.js"></script>
  <script src="popup.js"></script>
  <script src="../utils.js"></script>
  <script src="../content.js"></script>
</body>

</html>
